use std::env;
use std::path::Path;
use std::process::Command;

fn main() {
    let args: Vec<String> = env::args().collect();

    if args.len() < 2 {
        eprintln!("用法: 拖拽视频到本程序上即可运行");
        return;
    }

    for input_file in &args[1..] {
        let path = Path::new(input_file);

        if !path.exists() {
            eprintln!("文件不存在: {}", input_file);
            continue;
        }

        // 输出文件名 = 输入名 + "_min"
        let stem = path.file_stem().unwrap().to_string_lossy();
        let ext = path.extension().unwrap_or_default().to_string_lossy();
        let output_file = format!("{}_min.{}", stem, ext);

        // 获取视频时长
        let probe = Command::new("ffprobe")
            .args([
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                input_file,
            ])
            .output()
            .expect("ffprobe 运行失败");

        let duration_str = String::from_utf8_lossy(&probe.stdout);
        let duration: f64 = duration_str.trim().parse().unwrap_or(0.0);
        if duration <= 0.0 {
            eprintln!("无法获取视频时长: {}", input_file);
            continue;
        }

        // 计算目标比特率
        let target_size_bits = 10.0 * 8.0 * 1024.0 * 1024.0; // 10MB
        let bitrate = (target_size_bits / duration) as i32;

        println!("压缩中: {} -> {}", input_file, output_file);

        // 调用 ffmpeg
        let status = Command::new("ffmpeg")
            .args([
                "-y",
                "-i", input_file,
                "-c:v", "hevc_amf", // AMD 硬件加速 HEVC
                "-b:v", &format!("{}", bitrate),
                "-maxrate", &format!("{}", bitrate),
                "-bufsize", &format!("{}", bitrate),
                "-c:a", "copy", // 音频拷贝
                &output_file,
            ])
            .status()
            .expect("ffmpeg 运行失败");

        if status.success() {
            println!("完成: {}", output_file);
        } else {
            eprintln!("压缩失败: {}", input_file);
        }
    }
}
